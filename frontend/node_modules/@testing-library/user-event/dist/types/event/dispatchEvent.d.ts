import { type Instance } from '../setup';
import { EventType, EventTypeInit } from './types';
export declare function dispatchUIEvent<K extends EventType>(this: Instance, target: Element, type: K, init?: EventTypeInit<K>, preventDefault?: boolean): boolean;
export declare function dispatchEvent(this: Instance, target: Element, event: Event, preventDefault?: boolean): boolean;
export declare function dispatchDOMEvent<K extends EventType>(target: Element, type: K, init?: EventTypeInit<K>): void;
