{"version": 3, "file": "RuleCreator.js", "sourceRoot": "", "sources": ["../../src/eslint-utils/RuleCreator.ts"], "names": [], "mappings": ";;AAuDA,kCAyBC;AAzED,iDAA8C;AA0C9C;;;;;GAKG;AACH,SAAgB,WAAW,CAAC,UAAwC;IAClE,oHAAoH;IACpH,2FAA2F;IAC3F,OAAO,SAAS,eAAe,CAG7B,EACA,IAAI,EACJ,IAAI,EACJ,GAAG,IAAI,EAC4C;QAInD,OAAO,UAAU,CAAsB;YACrC,IAAI,EAAE;gBACJ,GAAG,IAAI;gBACP,IAAI,EAAE;oBACJ,GAAG,IAAI,CAAC,IAAI;oBACZ,GAAG,EAAE,UAAU,CAAC,IAAI,CAAC;iBACtB;aACF;YACD,GAAG,IAAI;SACR,CAAC,CAAC;IACL,CAAC,CAAC;AACJ,CAAC;AAED;;;;;GAKG;AACH,SAAS,UAAU,CAGjB,EACA,MAAM,EACN,cAAc,EACd,IAAI,GACwC;IAI5C,OAAO;QACL,MAAM,CAAC,OAAmD;YACxD,MAAM,kBAAkB,GAAG,IAAA,2BAAY,EAAC,cAAc,EAAE,OAAO,CAAC,OAAO,CAAC,CAAC;YACzE,OAAO,MAAM,CAAC,OAAO,EAAE,kBAAkB,CAAC,CAAC;QAC7C,CAAC;QACD,cAAc;QACd,IAAI;KACL,CAAC;AACJ,CAAC;AAED,WAAW,CAAC,WAAW,GAAG,UAAU,CAAC"}