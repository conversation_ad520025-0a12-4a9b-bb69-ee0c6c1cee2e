import { Custom } from '@vitest/runner';
import '@vitest/runner/utils';
import { ap as BenchFunction, aq as BenchmarkAPI } from './reporters-w_64AS5f.js';
import { Options } from 'tinybench';

declare function getBenchOptions(key: Custom): Options;
declare function getBenchFn(key: Custom): BenchFunction;
declare const bench: BenchmarkAPI;

export { getBenchOptions as a, bench as b, getBenchFn as g };
