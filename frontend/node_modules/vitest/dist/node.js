export { b as VitestPackageInstaller, V as VitestPlugin, a as createMethodsRPC, c as createVitest, r as registerConsoleShortcuts, s as startVitest } from './vendor/cli-api.OdDWuB7Y.js';
export { p as parseCLI } from './vendor/cac.cdAtVkJZ.js';
export { B as BaseSequencer } from './vendor/index.-xs08BYx.js';
import 'pathe';
import './vendor/constants.5J7I254_.js';
import './vendor/coverage.E7sG1b3r.js';
import './vendor/index.GVFv9dZ0.js';
import 'node:console';
import 'vite';
import 'node:path';
import 'node:url';
import 'node:process';
import 'node:fs';
import 'node:worker_threads';
import './vendor/_commonjsHelpers.jjO7Zipk.js';
import 'os';
import 'path';
import './vendor/index.xL8XjTLv.js';
import 'util';
import 'stream';
import 'events';
import 'fs';
import 'picocolors';
import 'vite-node/client';
import '@vitest/snapshot/manager';
import 'vite-node/server';
import '@vitest/runner/utils';
import './vendor/base.5NT-gWu5.js';
import '@vitest/utils';
import './vendor/env.AtSIuHFg.js';
import 'std-env';
import './path.js';
import './vendor/index.8bPxjt7g.js';
import 'zlib';
import 'buffer';
import 'crypto';
import 'https';
import 'http';
import 'net';
import 'tls';
import 'url';
import '@vitest/utils/source-map';
import 'node:crypto';
import 'node:v8';
import 'node:os';
import 'node:events';
import 'tinypool';
import 'node:fs/promises';
import 'local-pkg';
import 'vite-node/utils';
import 'magic-string';
import 'acorn-walk';
import '@vitest/utils/ast';
import 'strip-literal';
import './vendor/utils.dEtNIEgr.js';
import 'node:module';
import './vendor/index.SMVOaj7F.js';
import './vendor/global.CkGT_TMy.js';
import 'node:readline';
import 'readline';
import './vendor/tasks.IknbGB2n.js';
import 'node:perf_hooks';
import './chunks/runtime-console.EO5ha7qv.js';
import 'node:stream';
import './vendor/date.Ns1pGd_X.js';
import 'execa';
import 'module';
import 'assert';
